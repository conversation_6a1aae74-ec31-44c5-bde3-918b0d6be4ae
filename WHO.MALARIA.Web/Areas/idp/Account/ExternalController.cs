﻿using IdentityModel;
using Duende.IdentityServer;
using Duende.IdentityServer.Services;
using Duende.IdentityServer.Stores;
using MediatR;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Text.Encodings.Web;
using System.Threading.Tasks;
using WHO.MALARIA.Domain.Commands;
using WHO.MALARIA.Domain.Constants;
using WHO.MALARIA.Domain.Dtos;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Domain.Models;
using WHO.MALARIA.Domain.Queries;
using WHO.MALARIA.Features;
using static WHO.MALARIA.Domain.Constants.Constants;
using Exception = System.Exception;

namespace WHO.MALARIA.Web.Areas.idp.Account
{
    [Area("idp")]
    public class ExternalController : Controller
    {
        private readonly IIdentityServerInteractionService _interaction;
        private readonly IClientStore _clientStore;
        private readonly IAuthenticationSchemeProvider _schemeProvider;
        private readonly UserManager<IdentityDto> _userManager;
        private readonly SignInManager<IdentityDto> _signInManager;
        private readonly IEventService _events;
        private readonly IOptions<IdentityOptions> _optionsAccessor;
        private readonly UrlEncoder _urlEncoder;
        private readonly IMediator _mediator;

        public ExternalController(
            IIdentityServerInteractionService interaction,
            IClientStore clientStore,
            IAuthenticationSchemeProvider schemeProvider,
            UserManager<IdentityDto> userManager,
            SignInManager<IdentityDto> signInManager,
            IEventService events,
            IOptions<IdentityOptions> optionsAccessor,
            UrlEncoder urlEncoder,
            IMediator mediator
            )
        {
            _interaction = interaction;
            _clientStore = clientStore;
            _schemeProvider = schemeProvider;
            _userManager = userManager;
            _signInManager = signInManager;
            _events = events;
            _optionsAccessor = optionsAccessor;
            _urlEncoder = urlEncoder;
            _mediator = mediator;
        }

        /// <summary>
        /// initiate roundtrip to external authentication provider
        /// </summary>
        [HttpGet]
        public IActionResult Challenge(string scheme, string returnUrl)
        {
            if (string.IsNullOrEmpty(returnUrl)) returnUrl = "~/";

            // validate returnUrl - either it is a valid OIDC URL or back to a local page
            if (Url.IsLocalUrl(returnUrl) == false && _interaction.IsValidReturnUrl(returnUrl) == false)
            {
                // user might have clicked on a malicious link - should be logged
                throw new Exception("invalid return URL");
            }

            // start challenge and roundtrip the return URL and scheme
            var props = new AuthenticationProperties
            {
                RedirectUri = "/idp/External/Callback",
                Items =
                {
                    { "returnUrl", returnUrl },
                    { "scheme", scheme },
                }
            };

            return Challenge(props, scheme);

        }

        /// <summary>
        /// Post processing of external authentication
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> Callback()
        {
            // read external identity from the temporary cookie
            var result = await HttpContext.AuthenticateAsync(IdentityServerConstants.ExternalCookieAuthenticationScheme);

            if (result?.Succeeded != true)
            {
                // Log more detailed error information for debugging
                var errorDetails = result?.Failure?.Message ?? "Unknown authentication failure";
                var authProperties = result?.Properties?.Items?.Count > 0
                    ? string.Join(", ", result.Properties.Items.Select(x => $"{x.Key}={x.Value}"))
                    : "No properties";

                Console.WriteLine($"DEBUG - External authentication failed: {errorDetails}");
                Console.WriteLine($"DEBUG - Auth properties: {authProperties}");
                Console.WriteLine($"DEBUG - Result status: Succeeded={result?.Succeeded}, Principal={result?.Principal?.Identity?.Name}");

                throw new Exception($"External authentication error: {errorDetails}");
            }

            //Returns to the home controller's index action method on successfull login
            string returnUrl = result.Properties.Items["returnUrl"] ?? "~/";

            // lookup our user and external provider info
            (IdentityDto identityDto, string provider, string providerUserId, IEnumerable<Claim> claims) = await FindUserFromExternalProvider(result);

            string email = identityDto?.Email ?? claims.Where(x => x.Type == ClaimTypes.Email).Select(z => z.Value).SingleOrDefault();
            bool isInternalWHOUser = (bool)(email?.EndsWith("@who.int", StringComparison.OrdinalIgnoreCase));

            // DEBUG: Log authentication details
            Console.WriteLine($"DEBUG - Email: {email}");
            Console.WriteLine($"DEBUG - IsWHOUser: {isInternalWHOUser}");
            Console.WriteLine($"DEBUG - IdentityDto found: {identityDto != null}");
            Console.WriteLine($"DEBUG - Provider: {provider}");
            Console.WriteLine($"DEBUG - ProviderUserId: {providerUserId}");

            //IF @who.int user logs in to the system and He/She does not present in the system then create its identity and user in to system's DB and
            //allow him/her to login to the system as WHO user role.
            if (identityDto == null && isInternalWHOUser)
            {
                GetIdentityQuery request = new GetIdentityQuery(new List<FilterCriteria> {
                    new FilterCriteria {
                        Field = "Email",
                        Operator = Constants.DbOperators.Equals,
                        Value = email
                    }
                });


                IEnumerable<IdentityDto> identities = await _mediator.Send(request);

                if (!identities.Any())
                {
                    identityDto = await _mediator.Send(new CreateIdentityWithUserCommand() { Email = email });
                }
            }
            // If user not present in database and is not internal who user then redirect to unregister screen
            else if (identityDto == null && !isInternalWHOUser)
            {
                Console.WriteLine("DEBUG - User not found in local database");

                // For development: auto-create user for known external users
                if (email == "<EMAIL>" || Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") == "Development")
                {
                    Console.WriteLine("DEBUG - Creating user identity for development");
                    identityDto = await _mediator.Send(new CreateIdentityWithUserCommand() { Email = email });
                    Console.WriteLine("DEBUG - User identity created successfully");
                }
                else
                {
                    Console.WriteLine("DEBUG - Redirecting to /unregistereduser - User not found and not WHO user");
                    return Redirect("/unregistereduser");
                }
            }

            // Signs in the specified user.
            await _signInManager.SignInAsync(identityDto, false, provider);

            // delete temporary cookie used during external authentication
            await HttpContext.SignOutAsync(IdentityServerConstants.ExternalCookieAuthenticationScheme);

            //If user's status is not active and is who internal user then redirect to deactivated who user screen
            if (!identityDto.Status && isInternalWHOUser)
            {
                Console.WriteLine("DEBUG - Redirecting to /deactivatedwhouser - WHO user not active");
                await SignOutCurrentUser();
                return Redirect("/deactivatedwhouser");
            }
            //If user's status is not active and is not who internal user then redirect to deactivated user screen
            else if (!identityDto.Status)
            {
                Console.WriteLine("DEBUG - Redirecting to /deactivateduser - External user not active");
                // Don't sign out the user completely - they need to remain authenticated to request country access
                // Create cookie to maintain user identity for the country access request process
                CreateCookie(identityDto);
                return Redirect("/deactivateduser");
            }
            else
            {
                HasUserActiveForAssignedCountriesCommand command = new HasUserActiveForAssignedCountriesCommand(identityDto.User.Id);

                bool hasUserActiveForAssignedCountries = await _mediator.Send(command);

                // If user don't have country assigned and user is not internal who user and user role is not WHO Admin then user redirect to inactivated user screen
                if (!hasUserActiveForAssignedCountries && !isInternalWHOUser && identityDto.User.UserType != (int)UserRoleEnum.WHOAdmin)
                {
                    Console.WriteLine("DEBUG - Redirecting to /inActivatedUser - No country access");
                    // Don't sign out the user completely - they need to remain authenticated to request country access
                    // Create cookie to maintain user identity for the country access request process
                    CreateCookie(identityDto);
                    return Redirect("/inActivatedUser");
                }
            }

            //if logged in user is who user and not any country access granted
            //identityDto.User.UserType == 0 for Who-Viewer
            if (isInternalWHOUser && identityDto.User.UserType == 0)
            {
                Console.WriteLine("DEBUG - Redirecting to /countryaccessrequest - WHO user with UserType 0");
                //If identityDto.Name is null then set identityDto.Username as a name
                identityDto.Name = identityDto?.Name ?? identityDto.Username;

                //When who user login then save user identity details into cookie
                CreateCookie(identityDto);
                return Redirect("/countryaccessrequest");
            }

            Console.WriteLine($"DEBUG - Successful login, redirecting to: {returnUrl}");
            return Redirect(returnUrl);
        }

        //Added user identity details into cookie
        protected void CreateCookie(IdentityDto identity)
        {
            Guid identityId = identity.Id;
            Guid userId = identity.User.Id;
            int userType = identity.User.UserType;
            bool isNewUser = false;
            string name = identity.Name;
            string email = identity.Email;

            string userInfoStringify = JsonConvert.SerializeObject(new
            {
                userId,
                identityId,
                isNewUser,
                name,
                email,
                userType,
            });

            HttpContext.Response.Cookies.Append(Constants.Common.UserInfoCookieName, Crypto.Encrypt(userInfoStringify));

        }

        [HttpGet]
        public IActionResult SignOut()
        {
            DeleteAllCookies();

            return SignOut(CookieAuthenticationDefaults.AuthenticationScheme, IdentityConstant.AzureActiveDirectory);
        }

        /// <summary>
        /// Sign out user and clear cookie
        /// </summary>
        private async Task SignOutCurrentUser()
        {
            if (User.Identity.IsAuthenticated)
            {
                await HttpContext.SignOutAsync(CookieAuthenticationDefaults.AuthenticationScheme);
                await _signInManager.SignOutAsync();
                await HttpContext.SignOutAsync(IdentityServerConstants.ExternalCookieAuthenticationScheme);
                // Delete all other cookies created at the time of login
                DeleteAllCookies();
            }
        }
        #region Private Methods

        private async Task<(IdentityDto identityDto, string provider, string providerUserId, IEnumerable<Claim> claims)> FindUserFromExternalProvider(AuthenticateResult result)
        {
            var externalUser = result.Principal;

            // try to determine the unique id of the external user (issued by the provider)
            // the most common claim type for that are the sub claim and the NameIdentifier
            // depending on the external provider, some other claim type might be used
            var userIdClaim = externalUser.FindFirst(JwtClaimTypes.Subject) ??
                              externalUser.FindFirst(ClaimTypes.Email) ??
                              throw new Exception("Unknown userid");

            var provider = result.Properties.Items["scheme"];
            var providerUserId = userIdClaim.Value;

            // find external user
            IdentityDto user = await _userManager.FindByLoginAsync(provider, providerUserId);

            return (user, provider, providerUserId, externalUser.Claims.ToList());
        }


        // if the external login is OIDC-based, there are certain things we need to preserve to make logout work
        // this will be different for WS-Fed, SAML2p or other protocols
        private void ProcessLoginCallback(AuthenticateResult externalResult, List<Claim> localClaims, AuthenticationProperties localSignInProps)
        {
            // if the external system sent a session id claim, copy it over
            // so we can use it for single sign-out
            var sid = externalResult.Principal.Claims.FirstOrDefault(x => x.Type == JwtClaimTypes.SessionId);
            if (sid != null)
            {
                localClaims.Add(new Claim(JwtClaimTypes.SessionId, sid.Value));
            }

            // if the external provider issued an id_token, we'll keep it for signout
            var idToken = externalResult.Properties.GetTokenValue("id_token");
            if (idToken != null)
            {
                localSignInProps.StoreTokens(new[] { new AuthenticationToken { Name = "id_token", Value = idToken } });
            }
        }

        /// <summary>
        /// set the cookie  used for refreshing the view component
        /// </summary>
        /// <param name="key">key (unique identifier)</param>
        /// <param name="value">value to store in cookie object</param>
        public void SetRefreshMenuCookie(string key, string value)
        {
            Response.Cookies.Append(key, value);
        }

        /// <summary>
        /// Delete all cookies for a user after log out.
        /// </summary>
        private void DeleteAllCookies()
        {
            foreach (var cookie in HttpContext.Request.Cookies)
            {
                Response.Cookies.Delete(cookie.Key);
            }
        }

        #endregion
    }
}
